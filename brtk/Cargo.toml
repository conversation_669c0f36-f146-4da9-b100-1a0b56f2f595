[package]
name = "brtk"
version = "0.1.0"
edition = "2021"

[features]
default = ["icon"]
icon = ["bevy/bevy_winit", "bevy/bevy_window"]

[dependencies]
bevy = { version = "0.16", default-features = false, features = [
    "bevy_asset",
] }

thiserror = "2" # This library provides a convenient derive macro for the standard library's std::error::Error trait.
directories = "6" # A tiny mid-level library that provides platform-specific standard locations of directories
# rand = "0.9" # Random number generators and other randomness functionality.
# rand_pcg = { version = "0.9", features = ["serde1"] } # Selected PCG random number generators
# regex = "1" # An implementation of regular expressions for Rust.
# ron = { version = "0.8", features = ["integer128"] } # Rusty Object Notation
serde = { version = "1", features = ["derive", "rc", "alloc"] } # A generic serialization/deserialization framework

# keep the following in sync with Bevy's dependencies
winit = { version = "0.30", default-features = false }
image = { version = "0.25", default-features = false }
