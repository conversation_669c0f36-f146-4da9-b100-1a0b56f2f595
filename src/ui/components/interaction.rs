use bevy::prelude::*;

/// Palette for widget interactions. Add this to an entity that supports
/// [`Interaction`]s, such as a button, to change its [`BackgroundColor`] based
/// on the current interaction state.
#[derive(Component, Debug, Reflect)]
#[reflect(Component)]
pub struct InteractionPalette {
    pub none: Color,
    pub hovered: Color,
    pub pressed: Color,
}

// #[derive(Resource, Asset, Clone, Reflect)]
// #[reflect(Resource)]
// struct InteractionAssets {
//     #[dependency]
//     hover: Handle<AudioSource>,
//     #[dependency]
//     click: Handle<AudioSource>,
// }

// impl FromWorld for InteractionAssets {
//     fn from_world(world: &mut World) -> Self {
//         let assets = world.resource::<AssetServer>();
//         Self {
//             hover: assets.load("audio/sound_effects/button_hover.ogg"),
//             click: assets.load("audio/sound_effects/button_click.ogg"),
//         }
//     }
// }

// fn play_on_hover_sound_effect(
//     trigger: Trigger<Pointer<Over>>,
//     mut commands: Commands,
//     interaction_assets: Option<Res<InteractionAssets>>,
//     interaction_query: Query<(), With<Interaction>>,
// ) {
//     let Some(interaction_assets) = interaction_assets else {
//         return;
//     };

//     if interaction_query.contains(trigger.target()) {
//         commands.spawn(sound_effect(interaction_assets.hover.clone()));
//     }
// }

// fn play_on_click_sound_effect(
//     trigger: Trigger<Pointer<Click>>,
//     mut commands: Commands,
//     interaction_assets: Option<Res<InteractionAssets>>,
//     interaction_query: Query<(), With<Interaction>>,
// ) {
//     let Some(interaction_assets) = interaction_assets else {
//         return;
//     };

//     if interaction_query.contains(trigger.target()) {
//         commands.spawn(sound_effect(interaction_assets.click.clone()));
//     }
// }
