[package]
publish = false
version = "0.1.0"
edition = "2024"
name    = "echos_in_the_dark"
authors = ["<PERSON> <lecoq<PERSON><PERSON><EMAIL>>"]
exclude = ["dist", "build", "assets", "credits"]

# Enable a small amount of optimization in the dev profile.
[profile.dev]
opt-level = 1

# Enable a large amount of optimization in the dev profile for dependencies.
[profile.dev.package."*"]
opt-level = 3

# Remove expensive debug assertions due to <https://github.com/bevyengine/bevy/issues/14291>
[profile.dev.package.wgpu-types]
debug-assertions = false

# This is used by trunk as it doesn't support custom profiles: https://github.com/trunk-rs/trunk/issues/605
# xbuild also uses this profile for building android AABs because I couldn't find a configuration for it
[profile.release]
opt-level     = "s"
lto           = true
codegen-units = 1
strip         = true

# Profile for distribution
[profile.dist]
inherits      = "release"
opt-level     = 3
lto           = true
codegen-units = 1
strip         = true

[features]
default = [
    # Default to a native dev build.
    "dev_native",
]
dev = [
    # Improve compile times for dev builds by linking Bevy as a dynamic library.
    "bevy/dynamic_linking",
    "bevy/bevy_dev_tools",
    "bevy/bevy_ui_debug",
]
dev_native = [
    "dev",
    # Enable asset hot reloading for native dev builds.
    "bevy/file_watcher",
    # Enable embedded asset hot reloading for native dev builds.
    "bevy/embedded_watcher",
]

[dependencies]
bevy = { version = "0.16", default-features = false, features = [
    "animation",
    "bevy_asset",
    "bevy_color",
    "bevy_core_pipeline",
    "bevy_gilrs",
    "bevy_gizmos",
    "bevy_gltf",
    "bevy_log",
    "bevy_mesh_picking_backend",
    "bevy_pbr",
    "bevy_picking",
    "bevy_render",
    "bevy_scene",
    "bevy_sprite_picking_backend",
    "bevy_sprite",
    "bevy_state",
    "bevy_text",
    "bevy_ui_picking_backend",
    "bevy_ui",
    "bevy_window",
    "bevy_winit",
    "custom_cursor",
    "default_font",
    "hdr",
    "multi_threaded",
    "png",
    "smaa_luts",
    "sysinfo_plugin",
    "tonemapping_luts",
    "wayland",
    "webgl2",
    "x11",
] }
bevy_ecs_tilemap = { version = "0.16", features = ["atlas"] }
bevy_asset_loader = { version = "0.23.0", features = [
    "2d",
    "standard_dynamic_assets",
    "progress_tracking",
] }
bevy_kira_audio = { version = "0.23.0", features = ["android_shared_stdcxx"] }
brtk = { path = "./brtk" }
iyes_progress = { version = "0.14.0" }

# Error handling
anyhow = { version = "1" }
# 'Small vector' optimization: store up to a small number of items on the stack
bitvec = { version = "1" }
# A fast random number generator
fastrand = { version = "2" }
rand     = { version = "0.9" }
# A generic serialization/deserialization framework
serde = { version = "1", features = ["derive", "rc", "alloc"] }
# Error handling
thiserror = { version = "2" }
# A native Rust encoder and decoder of TOML-formatted files and streams
toml = "0.8.22"

# Compile low-severity logs out of native builds for performance.
log = { version = "0.4", features = [
    "max_level_debug",
    "release_max_level_warn",
] }
# Compile low-severity logs out of web builds for performance.
tracing = { version = "0.1", features = [
    "max_level_debug",
    "release_max_level_warn",
] }

# keep the following in sync with Bevy's dependencies
winit = { version = "0.30", default-features = false }
image = { version = "0.25", default-features = false }

[build-dependencies]
embed-resource = "1"

[package.metadata.bevy_cli.release]
# Disable debug functionality in release builds.
default-features = false

[package.metadata.bevy_cli.web.dev]
# Disable native-only debug functionality in web builds.
default-features = false
features         = ["dev"]

[lints.rust]
# Mark `bevy_lint` as a valid `cfg`, as it is set when the Bevy linter runs.
unexpected_cfgs = { level = "warn", check-cfg = ["cfg(bevy_lint)"] }

[lints.clippy]
# Bevy supplies arguments to systems via dependency injection, so it's natural for systems to
# request more than 7 arguments, which would undesirably trigger this lint.
too_many_arguments = "allow"
# Queries may access many components, which would undesirably trigger this lint.
type_complexity = "allow"
# Make sure macros use their standard braces, such as `[]` for `bevy_ecs::children!`.
nonstandard_macro_braces = "warn"

# You can configure the warning levels of Bevy lints here. For a list of all lints, see:
# <https://thebevyflock.github.io/bevy_cli/bevy_lint/lints/>
[package.metadata.bevy_lint]
# panicking_methods = "deny"
pedantic = "warn"
