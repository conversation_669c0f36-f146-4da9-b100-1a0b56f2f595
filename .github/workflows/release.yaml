name: Release

on:
  # Trigger this workflow when a tag is pushed in the format `v1.2.3`.
  push:
    tags:
      # Pattern syntax: <https://docs.github.com/en/actions/using-workflows/workflow-syntax-for-github-actions#filter-pattern-cheat-sheet>.
      - "v[0-9]+.[0-9]+.[0-9]+*"
  # Trigger this workflow manually via workflow dispatch.
  workflow_dispatch:
    inputs:
      version:
        description: "Version number in the format `v1.2.3`"
        required: true
        type: string

concurrency:
  group: ${{ github.workflow }}-${{ github.ref || github.run_id }}
  cancel-in-progress: true

# Configure the release workflow by editing the following values.
env:
  # The base filename of the binary produced by `cargo build`.
  cargo_build_binary_name: my_game

  # The path to the assets directory.
  assets_path: assets

  # Whether to build and package a release for a given target platform.
  build_for_web: true
  build_for_linux: true
  build_for_windows: true
  build_for_macos: true

  # Whether to upload the packages produced by this workflow to a GitHub release.
  upload_to_github: true

  # The itch.io project to upload to in the format `user-name/project-name`.
  # There will be no upload to itch.io if this is commented out.
  #upload_to_itch: your-itch-username/my_game

  ############
  # ADVANCED #
  ############

  # The ID of the app produced by this workflow.
  # Applies to macOS releases.
  # Must contain only A-Z, a-z, 0-9, hyphen, and period: <https://developer.apple.com/documentation/bundleresources/information_property_list/cfbundleidentifier>.
  app_id: .my-game

  # The base filename of the binary in the package produced by this workflow.
  # Applies to Windows, macOS, and Linux releases.
  # Defaults to `cargo_build_binary_name` if commented out.
  #app_binary_name: my_game

  # The name of the `.zip` or `.dmg` file produced by this workflow.
  # Defaults to `app_binary_name` if commented out.
  #app_package_name: my-game

  # The display name of the app produced by this workflow.
  # Applies to macOS releases.
  # Defaults to `app_package_name` if commented out.
  #app_display_name: My Game

  # The short display name of the app produced by this workflow.
  # Applies to macOS releases.
  # Must be 15 or fewer characters: <https://developer.apple.com/documentation/bundleresources/information_property_list/cfbundlename>.
  # Defaults to `app_display_name` if commented out.
  #app_short_name: My Game

  # Before enabling LFS, please take a look at GitHub's documentation for costs and quota limits:
  # <https://docs.github.com/en/repositories/working-with-files/managing-large-files/about-storage-and-bandwidth-usage>
  git_lfs: false

  # Enabling this only helps with consecutive releases to the same tag (and takes up cache storage space).
  # See: <https://github.com/orgs/community/discussions/27059>.
  use_github_cache: false

jobs:
  # Forward some environment variables as outputs of this job.
  # This is needed because the `env` context can't be used in the `if:` condition of a job:
  # <https://docs.github.com/en/actions/learn-github-actions/contexts#context-availability>
  forward-env:
    runs-on: ubuntu-latest
    steps:
      - name: Do nothing
        run: "true"
    outputs:
      upload_to_itch: ${{ env.upload_to_itch }}

  # Determine the version number for this workflow.
  get-version:
    runs-on: ubuntu-latest
    steps:
      - name: Determine version number
        id: tag
        run: echo "tag=${GITHUB_REF#refs/tags/}" >> "${GITHUB_OUTPUT}"
    outputs:
      # Use the input from workflow dispatch, or fall back to the git tag.
      version: ${{ inputs.version || steps.tag.outputs.tag }}

  # Build and package a release for each platform.
  build:
    needs:
      - get-version
    env:
      version: ${{ needs.get-version.outputs.version }}
    strategy:
      matrix:
        include:
          - platform: web
            targets: wasm32-unknown-unknown
            package_ext: .zip
            runner: ubuntu-latest

          - platform: linux
            targets: x86_64-unknown-linux-gnu
            package_ext: .zip
            runner: ubuntu-latest

          - platform: windows
            targets: x86_64-pc-windows-msvc
            binary_ext: .exe
            package_ext: .zip
            runner: windows-latest

          - platform: macos
            targets: x86_64-apple-darwin aarch64-apple-darwin
            app_suffix: .app/Contents/MacOS
            package_ext: .dmg
            runner: macos-latest
    runs-on: ${{ matrix.runner }}
    permissions:
      # Required to create a GitHub release: <https://docs.github.com/en/rest/releases/releases#create-a-release>.
      contents: write
    defaults:
      run:
        shell: bash

    steps:
      - name: Set up environment
        run: |
          # Default values:
          echo "app_binary_name=${app_binary_name:=${{ env.cargo_build_binary_name }}}" >> "${GITHUB_ENV}"
          echo "app_package_name=${app_package_name:=${app_binary_name}}" >> "${GITHUB_ENV}"
          echo "app_display_name=${app_display_name:=${app_package_name}}" >> "${GITHUB_ENV}"
          echo "app_short_name=${app_short_name:=${app_display_name}}" >> "${GITHUB_ENV}"

          # File paths:
          echo "app=tmp/app/${app_package_name}"'${{ matrix.app_suffix }}' >> "${GITHUB_ENV}"
          echo "package=${app_package_name}-"'${{ matrix.platform }}${{ matrix.package_ext }}' >> "${GITHUB_ENV}"

          # macOS environment:
          if [ '${{ matrix.platform }}' = 'macos' ]; then
            echo 'MACOSX_DEPLOYMENT_TARGET=11.0' >> "${GITHUB_ENV}" # macOS 11.0 Big Sur is the first version to support universal binaries.
            echo "SDKROOT=$(xcrun --sdk macosx --show-sdk-path)" >> "${GITHUB_ENV}"
          fi

          # Check if building for this platform is enabled.
          echo 'is_platform_enabled=${{
            (matrix.platform == 'web' && env.build_for_web == 'true') ||
            (matrix.platform == 'linux' && env.build_for_linux == 'true') ||
            (matrix.platform == 'windows' && env.build_for_windows == 'true') ||
            (matrix.platform == 'macos' && env.build_for_macos == 'true')
          }}' >> "${GITHUB_ENV}"

      - name: Checkout repository
        if: ${{ env.is_platform_enabled == 'true' }}
        uses: actions/checkout@v4
        with:
          lfs: ${{ env.git_lfs }}

      - name: Install Rust toolchain
        if: ${{ env.is_platform_enabled == 'true' }}
        uses: dtolnay/rust-toolchain@stable
        with:
          targets: ${{ matrix.targets }}

      - name: Restore Rust cache
        if: ${{ env.is_platform_enabled == 'true' && env.use_github_cache == 'true' }}
        uses: Swatinem/rust-cache@v2
        with:
          shared-key: release
          save-if: ${{ github.ref == 'refs/heads/main' }}

      - name: Install Bevy dependencies (Linux)
        if: ${{ env.is_platform_enabled == 'true' && matrix.platform == 'linux' }}
        run: sudo apt-get update; sudo apt-get install --no-install-recommends libasound2-dev libudev-dev libwayland-dev libxkbcommon-dev

      - name: Prepare output directories
        if: ${{ env.is_platform_enabled == 'true' }}
        run: rm -rf tmp; mkdir -p tmp/binary '${{ env.app }}'

      - name: Install Bevy CLI
        if: ${{ env.is_platform_enabled == 'true' }}
        run: cargo install --locked --git='https://github.com/TheBevyFlock/bevy_cli' bevy_cli

      - name: Install cargo-binstall (Web)
        if: ${{ env.is_platform_enabled == 'true' && matrix.platform == 'web' }}
        uses: cargo-bins/cargo-binstall@main

      - name: Install wasm-opt (Web)
        if: ${{ env.is_platform_enabled == 'true' && matrix.platform == 'web' }}
        run: cargo binstall --locked --no-confirm wasm-opt

      - name: Build and add web bundle to app (Web)
        if: ${{ env.is_platform_enabled == 'true' && matrix.platform == 'web' }}
        run: |
          bevy build --locked --release --features='${{ matrix.features }}' --yes web --bundle
          mv 'target/bevy_web/web-release/${{ env.cargo_build_binary_name }}' '${{ env.app }}'

      - name: Build and add binaries to app (non-Web)
        if: ${{ env.is_platform_enabled == 'true' && matrix.platform != 'web' }}
        run: |
          for target in ${{ matrix.targets }}; do
            bevy build --locked --release --target="${target}" --features='${{ matrix.features }}'
            mv target/"${target}"/release/'${{ env.cargo_build_binary_name }}${{ matrix.binary_ext }}' tmp/binary/"${target}"'${{ matrix.binary_ext }}'
          done
          if [ '${{ matrix.platform }}' = 'macos' ]; then
            lipo tmp/binary/*'${{ matrix.binary_ext }}' -create -output '${{ env.app }}/${{ env.app_binary_name }}${{ matrix.binary_ext }}'
          else
            mv tmp/binary/*'${{ matrix.binary_ext }}' '${{ env.app }}/${{ env.app_binary_name }}${{ matrix.binary_ext }}'
          fi

      - name: Add assets to app (non-Web)
        if: ${{ env.is_platform_enabled == 'true' && matrix.platform != 'web' }}
        run: cp -r ./'${{ env.assets_path }}' '${{ env.app }}' || true # Ignore error if assets folder does not exist.

      - name: Add metadata to app (macOS)
        if: ${{ env.is_platform_enabled == 'true' && matrix.platform == 'macos' }}
        run: |
          cat >'${{ env.app }}/../Info.plist' <<EOF
            <?xml version="1.0" encoding="UTF-8"?>
            <!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
            <plist version="1.0">
                <dict>
                    <key>CFBundleDevelopmentRegion</key>
                    <string>en</string>
                    <key>CFBundleDisplayName</key>
                    <string>${{ env.app_display_name }}</string>
                    <key>CFBundleExecutable</key>
                    <string>${{ env.app_binary_name }}</string>
                    <key>CFBundleIdentifier</key>
                    <string>${{ env.app_id }}</string>
                    <key>CFBundleName</key>
                    <string>${{ env.app_short_name }}</string>
                    <key>CFBundleShortVersionString</key>
                    <string>${{ env.version }}</string>
                    <key>CFBundleVersion</key>
                    <string>${{ env.version }}</string>
                    <key>CFBundleInfoDictionaryVersion</key>
                    <string>6.0</string>
                    <key>CFBundlePackageType</key>
                    <string>APPL</string>
                    <key>CFBundleSupportedPlatforms</key>
                    <array>
                        <string>MacOSX</string>
                    </array>
                </dict>
            </plist>
          EOF

      - name: Package app (non-Windows)
        if: ${{ env.is_platform_enabled == 'true' && matrix.platform != 'windows' }}
        working-directory: tmp/app
        run: |
          if [ '${{ matrix.platform }}' = 'macos' ]; then
            ln -s /Applications .
            hdiutil create -fs HFS+ -volname '${{ env.app_package_name }}' -srcfolder . '${{ env.package }}'
          else
            zip --recurse-paths '${{ env.package }}' '${{ env.app_package_name }}'
          fi

      - name: Package app (Windows)
        if: ${{ env.is_platform_enabled == 'true' && matrix.platform == 'windows' }}
        working-directory: tmp/app
        shell: pwsh
        run: Compress-Archive -Path '${{ env.app_package_name }}' -DestinationPath '${{ env.package }}'

      - name: Upload package to workflow artifacts
        if: ${{ env.is_platform_enabled == 'true' }}
        uses: actions/upload-artifact@v4
        with:
          path: tmp/app/${{ env.package }}
          name: package-${{ matrix.platform }}
          retention-days: 1

      - name: Upload package to GitHub release
        if: ${{ env.is_platform_enabled == 'true' && env.upload_to_github == 'true' }}
        uses: svenstaro/upload-release-action@v2
        with:
          repo_token: ${{ secrets.GITHUB_TOKEN }}
          file: tmp/app/${{ env.package }}
          asset_name: ${{ env.package }}
          release_name: ${{ env.version }}
          tag: ${{ env.version }}
          overwrite: true

  # Upload all packages to itch.io.
  upload-to-itch:
    runs-on: ubuntu-latest
    needs:
      - forward-env
      - get-version
      - build
    if: ${{ needs.forward-env.outputs.upload_to_itch != '' }}

    steps:
      - name: Download all packages
        uses: actions/download-artifact@v4
        with:
          pattern: package-*
          path: tmp

      - name: Install butler
        run: |
          curl -L -o butler.zip 'https://broth.itch.zone/butler/linux-amd64/LATEST/archive/default'
          unzip butler.zip
          chmod +x butler
          ./butler -V

      - name: Upload all packages to itch.io
        env:
          BUTLER_API_KEY: ${{ secrets.BUTLER_CREDENTIALS }}
        run: |
          for channel in $(ls tmp); do
            ./butler push \
              --fix-permissions \
              --userversion='${{ needs.get-version.outputs.version }}' \
              tmp/"${channel}"/* \
              '${{ env.upload_to_itch }}':"${channel#package-}"
          done
