name: CI

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref || github.run_id }}
  cancel-in-progress: true

env:
  # Reduce compile time and cache size.
  RUSTFLAGS: -Dwarnings -Zshare-generics=y -Zthreads=0 -Cdebuginfo=line-tables-only
  RUSTDOCFLAGS: -Dwarnings -Zshare-generics=y -Zthreads=0
  TOOLCHAIN: nightly


jobs:
  test:
    strategy:
      matrix:
        os: [windows-latest, ubuntu-latest, macos-latest]
    runs-on: ${{ matrix.os }}
    steps:
      - uses: actions/checkout@v4
      - uses: actions/cache@v4
        with:
          path: |
            ~/.cargo/bin/
            ~/.cargo/registry/index/
            ~/.cargo/registry/cache/
            ~/.cargo/git/db/
            target/
          key: ${{ runner.os }}-cargo-test-${{ hashFiles('**/Cargo.toml') }}

      - uses: dtolnay/rust-toolchain@master
        with:
          toolchain: ${{ env.TOOLCHAIN }}

      - name: Install alsa and udev
        run: sudo apt-get update; sudo apt-get install --no-install-recommends libasound2-dev libudev-dev
        if: runner.os == 'linux'
      - name: Build & run tests
        run: cargo test

  all-doc-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/cache@v4
        with:
          path: |
            ~/.cargo/bin/
            ~/.cargo/registry/index/
            ~/.cargo/registry/cache/
            ~/.cargo/git/db/
            target/
          key: ubuntu-latest-cargo-all-doc-tests-${{ hashFiles('**/Cargo.toml') }}
      - uses: dtolnay/rust-toolchain@master
        with:
          toolchain: ${{ env.TOOLCHAIN }}

      - name: Install alsa and udev
        run: sudo apt-get update; sudo apt-get install --no-install-recommends libasound2-dev libudev-dev
      - name: Run doc tests with all features (this also compiles README examples)
        run: cargo test --doc --all-features

  lint:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/cache@v4
        with:
          path: |
            ~/.cargo/bin/
            ~/.cargo/registry/index/
            ~/.cargo/registry/cache/
            ~/.cargo/git/db/
            target/
          key: ubuntu-latest-cargo-lint-${{ hashFiles('**/Cargo.toml') }}
      - uses: dtolnay/rust-toolchain@master
        with:
          toolchain: ${{ env.TOOLCHAIN }}
          components: rustfmt, clippy

      - name: Install alsa and udev
        run: sudo apt-get update; sudo apt-get install --no-install-recommends libasound2-dev libudev-dev

      - name: Run clippy
        run: cargo clippy --workspace --all-targets --all-features -- -Dwarnings

      - name: Check format
        run: cargo fmt --all -- --check

  # Run Bevy lints.
  bevy-lints:
    name: Bevy lints
    runs-on: ubuntu-latest
    timeout-minutes: 20
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Install Rust toolchain (plus bevy_lint)
        uses: TheBevyFlock/bevy_cli/bevy_lint@lint-v0.3.0

      - name: Restore Rust cache
        uses: Swatinem/rust-cache@v2
        with:
          shared-key: dev
          save-if: ${{ github.ref == 'refs/heads/main' }}

      - name: Install Bevy dependencies
        run: sudo apt-get update; sudo apt-get install --no-install-recommends libasound2-dev libudev-dev libwayland-dev

      - name: Run Bevy lints
        run: bevy_lint --locked --workspace --all-targets --all-features
