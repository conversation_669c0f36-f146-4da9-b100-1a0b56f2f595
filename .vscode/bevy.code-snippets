{"Bevy: New top-level function Plugin": {"scope": "rust", "prefix": "plugin", "body": ["use bevy::prelude::*;", "", "pub(super) fn plugin(app: &mut App) {", "\t$0", "}"]}, "Bevy: New Component": {"scope": "rust", "prefix": "component", "body": ["#[derive(Component, Reflect, Debug)]", "#[reflect(Component)]", "struct $1;"]}, "Bevy: New Resource": {"scope": "rust", "prefix": "resource", "body": ["#[derive(Resource, Reflect, Debug, Default)]", "#[reflect(Resource)]", "struct $1;"]}, "Bevy: New Event": {"scope": "rust", "prefix": "event", "body": ["#[derive(Event, Debug)]", "struct $1;"]}, "Bevy: New SystemSet": {"scope": "rust", "prefix": "systemset", "body": ["#[derive(SystemSet, <PERSON><PERSON>, <PERSON>lone, Eq, PartialEq, Hash, Debug)]", "enum $1 {", "\t$0", "}"]}, "Bevy: New Schedule": {"scope": "rust", "prefix": "schedule", "body": ["#[derive(<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>q, <PERSON><PERSON>, <PERSON>bu<PERSON>)]", "struct $1;"]}, "Bevy: New States": {"scope": "rust", "prefix": "states", "body": ["#[derive(<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>q, <PERSON>ial<PERSON>q, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)]", "enum $1 {", "\t#[default]", "\t$0", "}"]}}