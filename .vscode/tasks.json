{"version": "2.0.0", "tasks": [{"label": "Run native dev", "type": "process", "command": "bevy", "args": ["run"], "options": {"env": {"RUST_BACKTRACE": "full"}}, "presentation": {"clear": true}, "problemMatcher": ["$rustc"], "group": {"kind": "build", "isDefault": true}}, {"label": "Run native release", "type": "process", "command": "bevy", "args": ["run", "--release"], "presentation": {"clear": true}, "problemMatcher": ["$rustc"], "group": "build"}, {"label": "Run web dev", "type": "process", "command": "bevy", "args": ["run", "--yes", "web"], "options": {"env": {"RUST_BACKTRACE": "full"}}, "presentation": {"clear": true}, "problemMatcher": ["$rustc"], "group": "build"}, {"label": "Run web release", "type": "process", "command": "bevy", "args": ["run", "--yes", "--release", "web"], "presentation": {"clear": true}, "problemMatcher": ["$rustc"], "group": "build"}]}